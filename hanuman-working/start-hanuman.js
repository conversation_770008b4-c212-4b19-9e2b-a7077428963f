#!/usr/bin/env node

/**
 * 🐒 Script de Démarrage d'Hanuman
 * Lance le serveur Next.js avec bénédiction divine
 */

const { spawn } = require('child_process');
const path = require('path');

console.log('🕉️ ========================================');
console.log('🐒 DÉMARRAGE HANUMAN DIVINE');
console.log('🕉️ ========================================');

console.log('\n🙏 Invocation des bénédictions divines...');
console.log('🌅 AUM BRAHMAYE NAMAHA - Énergie créatrice');
console.log('🌊 AUM VISHNAVE NAMAHA - Énergie conservatrice');
console.log('🔥 AUM SHIVAYA NAMAHA - Énergie transformatrice');
console.log('🐒 AUM HANUMATE NAMAHA - Unité divine');

console.log('\n🚀 Lancement du serveur Next.js...');

// Chemin vers Next.js
const nextPath = path.join(__dirname, 'node_modules', '.bin', 'next');

// Lancement du serveur
const server = spawn('node', [path.join(__dirname, 'node_modules', 'next', 'dist', 'bin', 'next'), 'dev'], {
  stdio: 'inherit',
  cwd: __dirname
});

server.on('error', (error) => {
  console.error('❌ Erreur lors du démarrage:', error.message);
  process.exit(1);
});

server.on('close', (code) => {
  console.log(`\n🕉️ Serveur Hanuman arrêté avec le code ${code}`);
  console.log('🙏 AUM HANUMATE NAMAHA - Bénédiction divine');
});

// Gestion des signaux
process.on('SIGINT', () => {
  console.log('\n🛑 Arrêt demandé...');
  console.log('🙏 Bénédiction divine avant fermeture');
  server.kill('SIGINT');
});

process.on('SIGTERM', () => {
  console.log('\n🛑 Arrêt forcé...');
  server.kill('SIGTERM');
});
